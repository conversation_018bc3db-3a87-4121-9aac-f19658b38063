import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Keyboard, HelpCircle } from "lucide-react";
import { useKeyboardNavigation } from "@/hooks/useKeyboardNavigation";

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  description: string;
}

interface KeyboardShortcutsHelpProps {
  shortcuts: KeyboardShortcut[];
  trigger?: React.ReactNode;
}

export function KeyboardShortcutsHelp({ 
  shortcuts, 
  trigger 
}: KeyboardShortcutsHelpProps) {
  const [open, setOpen] = useState(false);

  // Adicionar atalho para abrir a ajuda
  useKeyboardNavigation({
    shortcuts: [{
      key: '?',
      shiftKey: true,
      action: () => setOpen(true),
      description: 'Shift+?: Mostrar atalhos'
    }],
    enabled: !open
  });

  const formatShortcut = (shortcut: KeyboardShortcut) => {
    const keys = [];
    
    if (shortcut.ctrlKey) keys.push('Ctrl');
    if (shortcut.altKey) keys.push('Alt');
    if (shortcut.shiftKey) keys.push('Shift');
    keys.push(shortcut.key);
    
    return keys.join(' + ');
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Keyboard className="h-4 w-4 mr-2" />
      Atalhos
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Atalhos do Teclado
          </DialogTitle>
          <DialogDescription>
            Use estes atalhos para navegar mais rapidamente
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {shortcuts.map((shortcut, index) => (
            <div key={index} className="flex items-center justify-between gap-3">
              <span className="text-sm text-muted-foreground flex-1">
                {shortcut.description}
              </span>
              <Badge variant="secondary" className="font-mono text-xs">
                {formatShortcut(shortcut)}
              </Badge>
            </div>
          ))}
          
          {/* Atalho padrão para abrir ajuda */}
          <div className="flex items-center justify-between gap-3 pt-2 border-t">
            <span className="text-sm text-muted-foreground flex-1">
              Mostrar esta ajuda
            </span>
            <Badge variant="secondary" className="font-mono text-xs">
              Shift + ?
            </Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Componente para mostrar atalhos no canto da tela
export function KeyboardShortcutsIndicator({ 
  shortcuts 
}: { 
  shortcuts: KeyboardShortcut[] 
}) {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <KeyboardShortcutsHelp 
        shortcuts={shortcuts}
        trigger={
          <Button variant="outline" size="sm" className="shadow-lg">
            <HelpCircle className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
}
