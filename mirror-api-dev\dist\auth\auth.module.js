"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const env_service_1 = require("../env/env.service");
const env_module_1 = require("../env/env.module");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const mail_module_1 = require("../mail/mail.module");
const auth_guard_1 = require("./auth.guard");
const permission_service_1 = require("./permission.service");
const permission_controller_1 = require("./permission.controller");
const role_service_1 = require("./role.service");
const role_controller_1 = require("./role.controller");
const users_module_1 = require("../users/users.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            users_module_1.UsersModule,
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [env_module_1.EnvModule],
                inject: [env_service_1.EnvService],
                global: true,
                useFactory(env) {
                    const privateKey = env.get("JWT_PRIVATE_KEY");
                    const publicKey = env.get("JWT_PUBLIC_KEY");
                    return {
                        privateKey: Buffer.from(privateKey, "base64"),
                        publicKey: Buffer.from(publicKey, "base64"),
                        signOptions: {
                            algorithm: "RS256",
                            expiresIn: "1h",
                            issuer: env.get("APP_NAME"),
                        },
                    };
                },
            }),
            mail_module_1.MailModule,
        ],
        providers: [
            env_service_1.EnvService,
            auth_service_1.AuthService,
            auth_guard_1.AuthGuard,
            permission_service_1.PermissionService,
            role_service_1.RoleService,
        ],
        controllers: [auth_controller_1.AuthController, permission_controller_1.PermissionController, role_controller_1.RoleController],
        exports: [auth_service_1.AuthService, auth_guard_1.AuthGuard],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map