"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const env_1 = require("./env");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {});
    const envService = app.get(env_1.EnvService);
    app.enableCors({
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
        origin: envService.get("APP_CORS").split(","),
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        errorHttpStatusCode: common_1.HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
            const result = errors?.map((error) => ({
                field: error.property,
                error: error?.constraints
                    ? Object.values(error.constraints).join(", ")
                    : [],
            }));
            return new common_1.BadRequestException({
                statusCode: common_1.HttpStatus.UNPROCESSABLE_ENTITY,
                message: result,
            });
        },
    }));
    const port = envService.get("PORT");
    await app.listen(port);
}
bootstrap();
//# sourceMappingURL=main.js.map