"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleService = void 0;
const prisma_service_1 = require("../prisma/prisma.service");
const common_1 = require("@nestjs/common");
let RoleService = class RoleService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async fetchAllRoles() {
        return this.prisma.role.findMany();
    }
    async getRoleById(id) {
        return this.prisma.role.findUnique({
            where: { id },
        });
    }
    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });
        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }
    async createRole(data) {
        await this.checkRoleIsUnique(data);
        return await this.prisma.role.create({
            data,
        });
    }
    async updateRole(id, data) {
        await this.checkRoleIsUnique(data, id);
        return await this.prisma.role.update({
            where: { id },
            data,
        });
    }
    async deleteRole(id) {
        return await this.prisma.role.delete({
            where: { id },
        });
    }
    async checkRoleIsUnique(data, id) {
        const { name, slug } = data;
        await this.prisma.role
            .findFirst({
            where: {
                name,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O nome informado já está em uso");
            }
        });
        await this.prisma.role
            .findFirst({
            where: {
                slug,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O slug informado já está em uso");
            }
        });
    }
};
exports.RoleService = RoleService;
exports.RoleService = RoleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RoleService);
//# sourceMappingURL=role.service.js.map