import { lazy } from "react";

// Lazy loading para melhor performance
const Home = lazy(() => import("@/pages/home").then(m => ({ default: m.Home })));
const ListUsers = lazy(() => import("@/pages/users").then(m => ({ default: m.ListUsers })));
const Profile = lazy(() => import("@/pages/users").then(m => ({ default: m.Profile })));
const ChangePassword = lazy(() => import("@/pages/users").then(m => ({ default: m.ChangePassword })));
const FormChat = lazy(() => import("@/pages/chats").then(m => ({ default: m.FormChat })));
const ListChats = lazy(() => import("@/pages/chats").then(m => ({ default: m.ListChats })));
const UpdateChats = lazy(() => import("@/pages/chats").then(m => ({ default: m.UpdateChats })));
const Database = lazy(() => import("@/pages/settings").then(m => ({ default: m.Database })));
const Instance = lazy(() => import("@/pages/settings").then(m => ({ default: m.Instance })));
const Roles = lazy(() => import("@/pages/settings").then(m => ({ default: m.Roles })));
const Params = lazy(() => import("@/pages/settings").then(m => ({ default: m.Params })));
const ListIncomingMessages = lazy(() => import("@/pages/chats").then(m => ({ default: m.ListIncomingMessages })));
const GroupsInsurances = lazy(() => import("@/pages/groups-insurances").then(m => ({ default: m.GroupsInsurances })));
const GroupsUsers = lazy(() => import("@/pages/groups-users").then(m => ({ default: m.GroupsUsers })));
const Permissions = lazy(() => import("@/pages/settings/permissions").then(m => ({ default: m.Permissions })));
const ListSentMessages = lazy(() => import("@/pages/chats/list-sent-messages").then(m => ({ default: m.ListSentMessages })));
const ListPositiveMessages = lazy(() => import("@/pages/chats/list-positive-messages").then(m => ({ default: m.ListPositiveMessages })));

export interface RoutesProps {
  path: string;
  element: JSX.Element;
  can: string[];
}

export const routes: RoutesProps[] = [
  { path: "/", element: <Home />, can: [] },
  { path: "/home", element: <Home />, can: [] },
  { path: "/me", element: <Profile />, can: [] },
  { path: "/users", element: <ListUsers />, can: ["r-user"] },
  { path: "/profile", element: <Profile />, can: [] },
  { path: "/change-password", element: <ChangePassword />, can: [] },
  { path: "/chat", element: <FormChat />, can: ["c-chat"] },
  { path: "/chat/:id", element: <FormChat />, can: ["u-chat"] },
  { path: "/chats", element: <ListChats />, can: ["r-chat", "user"] },
  { path: "/chats-update", element: <UpdateChats />, can: ["u-chat"] },
  { path: "/groups-insurances", element: <GroupsInsurances />, can: ["r-group"] },
  { path: "/groups-users", element: <GroupsUsers />, can: ["r-group"] },
  { path: "/settings/instance", element: <Instance />, can: ["admin", "manager"] },
  { path: "/settings/params", element: <Params />, can: ["admin", "manager"] },
  { path: "/settings/database", element: <Database />, can: ["admin"] },
  { path: "/settings/roles", element: <Roles />, can: ["admin"] },
  { path: "/settings/permissions", element: <Permissions />, can: ["admin"] },
  { path: "/chats/list-incoming-messages", element: <ListIncomingMessages />, can: [] },
  { path: "/chats/list-sent-messages", element: <ListSentMessages />, can: [] },
  { path: "/chats/list-positive-messages", element: <ListPositiveMessages />, can: [] },
];
