"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTransport = void 0;
const client_ses_1 = require("@aws-sdk/client-ses");
const getTransport = (env) => {
    if (env.get("MAIL_DRIVER") === "ses") {
        return {
            SES: {
                ses: new client_ses_1.SESClient({
                    region: env.get("AWS_REGION"),
                    credentials: {
                        accessKeyId: env.get("AWS_ACCESS_KEY_ID"),
                        secretAccessKey: env.get("AWS_SECRET_ACCESS_KEY"),
                    },
                }),
                aws: require("@aws-sdk/client-ses"),
            },
        };
    }
    return {
        host: env.get("MAIL_HOST"),
        port: +env.get("MAIL_PORT"),
        auth: {
            user: env.get("MAIL_USER"),
            pass: env.get("MAIL_PASS"),
        },
    };
};
exports.getTransport = getTransport;
//# sourceMappingURL=transport.config.js.map