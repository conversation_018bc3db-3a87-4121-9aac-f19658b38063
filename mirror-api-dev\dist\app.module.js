"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const auth_module_1 = require("./auth/auth.module");
const env_1 = require("./env");
const throttler_1 = require("@nestjs/throttler");
const prisma_module_1 = require("./prisma/prisma.module");
const mail_module_1 = require("./mail/mail.module");
const users_module_1 = require("./users/users.module");
const http_module_1 = require("./http/http.module");
const logger_module_1 = require("./logger/logger.module");
const cache_module_1 = require("./cache/cache.module");
const schedule_1 = require("@nestjs/schedule");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                validate: (env) => env_1.envSchema.parse(env),
                isGlobal: true,
            }),
            env_1.EnvModule,
            throttler_1.ThrottlerModule.forRootAsync({
                imports: [env_1.EnvModule],
                inject: [env_1.EnvService],
                useFactory: (env) => [
                    {
                        ttl: +env.get("THROTTLE_TTL"),
                        limit: +env.get("THROTTLE_LIMIT"),
                    },
                ],
            }),
            cache_module_1.CacheModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            prisma_module_1.PrismaModule,
            mail_module_1.MailModule,
            http_module_1.HttpModule,
            logger_module_1.LoggerModule,
            schedule_1.ScheduleModule.forRoot(),
        ],
        controllers: [],
        providers: [env_1.EnvService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map