"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheTasks_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheTasks = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const cache_service_1 = require("./cache.service");
const cache_config_1 = require("./cache.config");
let CacheTasks = CacheTasks_1 = class CacheTasks {
    cacheService;
    logger = new common_1.Logger(CacheTasks_1.name);
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    async handleCleanupExpiredCache() {
        this.logger.debug("Executando limpeza de cache expirado...");
        await this.cacheService.cleanupExpiredCache();
    }
};
exports.CacheTasks = CacheTasks;
__decorate([
    (0, schedule_1.Cron)(cache_config_1.cacheConfig.cronTime),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheTasks.prototype, "handleCleanupExpiredCache", null);
exports.CacheTasks = CacheTasks = CacheTasks_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], CacheTasks);
//# sourceMappingURL=cache.tasks.js.map