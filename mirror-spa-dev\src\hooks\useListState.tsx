import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { useDebounceValue } from "./useDebounceValue";

interface UseListStateProps<T> {
  queryKey: string[];
  queryFn: (params: ListParams) => Promise<T[]>;
  initialFilters?: Record<string, any>;
  searchFields?: (keyof T)[];
  sortField?: keyof T;
  sortDirection?: "asc" | "desc";
}

interface ListParams {
  search?: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    direction: "asc" | "desc";
  };
  page?: number;
  limit?: number;
}

export function useListState<T extends Record<string, any>>({
  queryKey,
  queryFn,
  initialFilters = {},
  searchFields = [],
  sortField,
  sortDirection = "asc",
}: UseListStateProps<T>) {
  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState(initialFilters);
  const [sort, setSort] = useState({
    field: sortField as string,
    direction: sortDirection,
  });
  const [selectedItems, setSelectedItems] = useState<T[]>([]);

  const debouncedSearch = useDebounceValue(search, 300);

  const params: ListParams = {
    search: debouncedSearch,
    filters,
    sort: sort.field ? sort : undefined,
  };

  const { data: rawData = [], isLoading, error, refetch } = useQuery({
    queryKey: [...queryKey, params],
    queryFn: () => queryFn(params),
  });

  // Filtros locais para dados já carregados
  const filteredData = useMemo(() => {
    let result = rawData;

    // Aplicar busca local se searchFields estiver definido
    if (debouncedSearch && searchFields.length > 0) {
      result = result.filter((item) =>
        searchFields.some((field) =>
          String(item[field])
            .toLowerCase()
            .includes(debouncedSearch.toLowerCase())
        )
      );
    }

    // Aplicar ordenação local
    if (sort.field) {
      result = [...result].sort((a, b) => {
        const aValue = a[sort.field];
        const bValue = b[sort.field];
        
        if (aValue < bValue) return sort.direction === "asc" ? -1 : 1;
        if (aValue > bValue) return sort.direction === "asc" ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [rawData, debouncedSearch, searchFields, sort]);

  const handleSort = useCallback((field: keyof T) => {
    setSort((prev) => ({
      field: field as string,
      direction: 
        prev.field === field && prev.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  const handleFilter = useCallback((key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
    setSearch("");
  }, [initialFilters]);

  const toggleSelectItem = useCallback((item: T) => {
    setSelectedItems((prev) => {
      const isSelected = prev.some((selected) => selected.id === item.id);
      if (isSelected) {
        return prev.filter((selected) => selected.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  }, []);

  const selectAllItems = useCallback(() => {
    setSelectedItems(filteredData);
  }, [filteredData]);

  const clearSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  return {
    // Dados
    data: filteredData,
    isLoading,
    error,
    refetch,
    
    // Busca
    search,
    setSearch,
    
    // Filtros
    filters,
    setFilters,
    handleFilter,
    clearFilters,
    
    // Ordenação
    sort,
    handleSort,
    
    // Seleção
    selectedItems,
    toggleSelectItem,
    selectAllItems,
    clearSelection,
    isItemSelected: (item: T) => 
      selectedItems.some((selected) => selected.id === item.id),
    
    // Estatísticas
    totalItems: filteredData.length,
    selectedCount: selectedItems.length,
  };
}
