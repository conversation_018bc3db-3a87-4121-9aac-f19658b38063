"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
exports.default = (0, config_1.registerAs)("jwt", () => ({
    privateKey: Buffer.from(process.env.JWT_PRIVATE_KEY || "", "base64"),
    publicKey: Buffer.from(process.env.JWT_PUBLIC_KEY || "", "base64"),
    signOptions: {
        algorithm: "RS256",
        expiresIn: process.env.JWT_EXPIRES_IN || "1h",
        issuer: process.env.APP_NAME || "apiNest",
    },
}));
//# sourceMappingURL=jwt.config.js.map