"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MailService = void 0;
const common_1 = require("@nestjs/common");
const mailer_1 = require("@nestjs-modules/mailer");
const env_1 = require("../env");
const prisma_service_1 = require("../prisma/prisma.service");
const uuid_1 = require("uuid");
let MailService = class MailService {
    mailerService;
    env;
    prisma;
    constructor(mailerService, env, prisma) {
        this.mailerService = mailerService;
        this.env = env;
        this.prisma = prisma;
    }
    async sendMail(to, subject, text) {
        const mailOptions = {
            to,
            subject,
            text,
        };
        try {
            await this.mailerService.sendMail(mailOptions);
            return true;
        }
        catch (error) {
            throw new Error("Failed to send email");
        }
    }
    async sendWelcomeUser(user) {
        const token = await this.createNewPasswordToken(user);
        const url = `${this.env.get("APP_URL")}/auth/new-password/${token}`;
        return this.mailerService.sendMail({
            to: user.email,
            subject: "Seja bem-vindo",
            template: "./welcome",
            context: {
                title: "Seja bem-vindo",
                name: user.first_name,
                url,
            },
        });
    }
    async sendTokenForget(user, token) {
        const url = `${this.env.get("APP_URL")}/auth/reset/${token}`;
        return this.mailerService.sendMail({
            to: user.email,
            subject: "Recuperação de senha",
            template: "./forget",
            context: {
                title: "Recuperação de senha",
                name: user.first_name,
                url,
            },
        });
    }
    async sendPasswordChanged(user) {
        return this.mailerService.sendMail({
            to: user.email,
            subject: "Senha Alterada",
            template: "./password-changed",
            context: {
                title: "Senha alterada",
                name: user.first_name,
            },
        });
    }
    async createNewPasswordToken(user) {
        const token = await this.prisma.userToken.create({
            data: {
                token: (0, uuid_1.v7)(),
                user_id: user.id,
                email: user.email,
                type: "NEW_PASSWORD",
                expires_at: new Date(Date.now() + 1000 * 60 * 60 * 24),
            },
        });
        return token.token;
    }
};
exports.MailService = MailService;
exports.MailService = MailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [mailer_1.MailerService,
        env_1.EnvService,
        prisma_service_1.PrismaService])
], MailService);
//# sourceMappingURL=mail.service.js.map