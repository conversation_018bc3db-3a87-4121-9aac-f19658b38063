"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Auth = void 0;
const common_1 = require("@nestjs/common");
exports.Auth = (0, common_1.createParamDecorator)((_, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    if (!request.auth) {
        throw new Error("O decorador Auth deve ser usado após o AuthGuard. Certifique se você aplicou o @UseGuards(AuthGuard) anteriormente.");
    }
    return { ...request.auth.user };
});
//# sourceMappingURL=auth.decorator.js.map