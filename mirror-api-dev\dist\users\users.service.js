"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const node_crypto_1 = require("node:crypto");
const bcryptjs_1 = require("bcryptjs");
const cache_service_1 = require("../cache/cache.service");
const me_service_1 = require("./me.service");
const mail_service_1 = require("../mail/mail.service");
let UsersService = class UsersService {
    prisma;
    cache;
    meService;
    mailService;
    constructor(prisma, cache, meService, mailService) {
        this.prisma = prisma;
        this.cache = cache;
        this.meService = meService;
        this.mailService = mailService;
    }
    async findAll() {
        const users = await this.prisma.user.findMany({
            include: {
                RoleUser: {
                    select: {
                        role: { select: { id: true, name: true } },
                    },
                },
            },
            orderBy: {
                first_name: "asc",
            },
        });
        return users.map((user) => ({
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone: user.phone,
            status: user.status,
            notify: user.notify,
            document: user.document,
            roles: user.RoleUser.map((role) => ({
                id: role.role.id,
                name: role.role.name,
            })),
        }));
    }
    async findById(id) {
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id },
            include: {
                RoleUser: {
                    select: {
                        role: true,
                    },
                },
            },
        });
        return {
            id: user.id,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone: user.phone,
            status: user.status,
            document: user.document,
            notify: user.notify,
            roles: user?.RoleUser.map((role) => ({
                id: role.role.id,
                name: role.role.name,
                slug: role.role.slug,
            })),
        };
    }
    async create(dto) {
        await this.verifyIfUserUnique(dto);
        const salt = await (0, bcryptjs_1.genSalt)(8);
        const password = await (0, bcryptjs_1.hash)((0, node_crypto_1.randomUUID)() + salt, 8);
        const { roles, ...data } = dto;
        const user = await this.prisma.user.create({
            data: { ...data, id: (0, node_crypto_1.randomUUID)(), salt, password },
        });
        if (roles) {
            await this.prisma.roleUser.createMany({
                data: roles.map((roleId) => ({
                    user_id: user.id,
                    role_id: roleId,
                })),
            });
        }
        await this.mailService.sendWelcomeUser(user);
        return this.findById(user.id);
    }
    async update(id, dto) {
        await this.verifyIfUserUnique(dto, id);
        const { roles, ...data } = dto;
        await this.cache.delete(`users:${id}`);
        await this.prisma.user.update({
            where: { id },
            data,
        });
        await this.prisma.roleUser.deleteMany({
            where: { user_id: id },
        });
        if (roles) {
            await this.prisma.roleUser.createMany({
                data: roles.map((roleId) => ({
                    user_id: id,
                    role_id: roleId,
                })),
            });
        }
        return this.findById(id);
    }
    async delete(id, auth_user_id) {
        if (process.env.NODE_ENV !== "development") {
            throw new common_1.BadRequestException("Você não pode excluir");
        }
        if (id === auth_user_id) {
            throw new common_1.BadRequestException("Vou não pode deletar sua conta");
        }
        await this.prisma.user.findUniqueOrThrow({
            where: { id },
        });
        await this.cache.delete(`users:${id}`);
        return this.prisma.user.delete({
            where: { id },
        });
    }
    async updatePassword(id, password) {
        const salt = await (0, bcryptjs_1.genSalt)(8);
        const hashPassword = await (0, bcryptjs_1.hash)(password + salt, 8);
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id },
        });
        await this.prisma.user.update({
            where: { id },
            data: { password: hashPassword, salt },
        });
        await this.mailService.sendPasswordChanged(user);
        return {
            message: "Password updated successfully",
        };
    }
    async findMe(id) {
        return this.meService.findMe(id);
    }
    async verifyIfUserUnique(dto, id) {
        const { email, document } = dto;
        await this.prisma.user
            .findFirst({
            where: {
                email,
                ...(id && { id: { not: id } }),
            },
        })
            .then((user) => {
            if (user) {
                throw new common_1.BadRequestException("o email informado já está em uso");
            }
        });
        await this.prisma.user
            .findFirst({
            where: {
                document,
                ...(id && { id: { not: id } }),
            },
        })
            .then((user) => {
            if (user) {
                throw new common_1.BadRequestException("o documento informado já está em uso");
            }
        });
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        cache_service_1.CacheService,
        me_service_1.MeService,
        mail_service_1.MailService])
], UsersService);
//# sourceMappingURL=users.service.js.map