"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const cache_service_1 = require("../cache/cache.service");
let MeService = class MeService {
    prisma;
    cache;
    constructor(prisma, cache) {
        this.prisma = prisma;
        this.cache = cache;
    }
    async findMe(user_id) {
        const cache = await this.cache.get(`user:${user_id}`);
        if (cache) {
            return cache;
        }
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id: user_id },
            select: {
                id: true,
                first_name: true,
                last_name: true,
                email: true,
                phone: true,
                avatar: true,
                document: true,
                status: true,
                notify: true,
            },
        });
        const slugs = await this.prisma.$queryRaw `
        /*busca os slugs dos cargos e permissões do usuário*/
        SELECT r.slug as slugs
        FROM role_user ru
        JOIN roles r ON ru.role_id = r.id
        WHERE ru.user_id = ${user_id}
        UNION
        SELECT p.slug as slugs
        FROM role_user ru
        JOIN permission_role pr ON ru.role_id = pr.role_id
        JOIN permissions p ON pr.permission_id = p.id
        WHERE ru.user_id = ${user_id}
        `;
        const value = {
            ...user,
            acl: slugs.map((slug) => slug.slugs),
        };
        await this.cache.set({
            key: `user:${user_id}`,
            value,
            ttl: 60 * 10 * 1000,
        });
        return value;
    }
};
exports.MeService = MeService;
exports.MeService = MeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        cache_service_1.CacheService])
], MeService);
//# sourceMappingURL=me.service.js.map