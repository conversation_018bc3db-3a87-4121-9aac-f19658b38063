"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthResetDTO = void 0;
const class_validator_1 = require("class-validator");
const match_decorator_1 = require("../../decorators/match.decorator");
class AuthResetDTO {
    token;
    password;
    password_confirm;
}
exports.AuthResetDTO = AuthResetDTO;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthResetDTO.prototype, "token", void 0);
__decorate([
    (0, class_validator_1.IsStrongPassword)(),
    __metadata("design:type", String)
], AuthResetDTO.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, match_decorator_1.Match)("password", { message: "Passwords do not match" }),
    __metadata("design:type", String)
], AuthResetDTO.prototype, "password_confirm", void 0);
//# sourceMappingURL=auth-reset.dto.js.map