"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Can = void 0;
const common_1 = require("@nestjs/common");
const auth_guard_1 = require("./auth.guard");
const Can = (...acl) => {
    return (0, common_1.applyDecorators)((0, common_1.UseGuards)(auth_guard_1.AuthGuard), (0, common_1.SetMetadata)("acl", acl));
};
exports.Can = Can;
//# sourceMappingURL=can.decorator.js.map