"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionController = void 0;
const common_1 = require("@nestjs/common");
const permission_service_1 = require("./permission.service");
const auth_guard_1 = require("./auth.guard");
const auth_decorator_1 = require("./auth.decorator");
const throttler_1 = require("@nestjs/throttler");
const auth_teste_dto_1 = require("./dto/auth-teste.dto");
const permission_create_dto_1 = require("./dto/permission-create.dto");
const enums_1 = require("../enums");
let PermissionController = class PermissionController {
    service;
    constructor(service) {
        this.service = service;
    }
    async permissions() {
        return this.service.fetchAllPermissions();
    }
    async createPermission(dto) {
        return this.service.createPermission(dto);
    }
    async updatePermission(id, dto) {
        return this.service.updatePermission(id, dto);
    }
    async deletePermission(id) {
        return this.service.deletePermission(id);
    }
    async teste(auth) {
        return { auth };
    }
};
exports.PermissionController = PermissionController;
__decorate([
    (0, common_1.Get)("permissions"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "permissions", null);
__decorate([
    (0, common_1.Post)("permissions"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [permission_create_dto_1.PermissionCreateDTO]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "createPermission", null);
__decorate([
    (0, common_1.Put)("permissions/:id"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, permission_create_dto_1.PermissionCreateDTO]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "updatePermission", null);
__decorate([
    (0, common_1.Delete)("permissions/:id"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "deletePermission", null);
__decorate([
    (0, common_1.Post)("teste"),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard, auth_guard_1.AuthGuard),
    __param(0, (0, auth_decorator_1.Auth)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_teste_dto_1.AuthTesteDTO]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "teste", null);
exports.PermissionController = PermissionController = __decorate([
    (0, common_1.Controller)("auth"),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    (0, auth_guard_1.Can)(enums_1.acl.admin),
    __metadata("design:paramtypes", [permission_service_1.PermissionService])
], PermissionController);
//# sourceMappingURL=permission.controller.js.map