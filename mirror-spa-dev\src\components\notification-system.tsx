import { toast } from "sonner";
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react";

export type NotificationType = "success" | "error" | "warning" | "info";

interface NotificationOptions {
  title?: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const icons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
};

const colors = {
  success: "text-green-500",
  error: "text-red-500", 
  warning: "text-yellow-500",
  info: "text-blue-500",
};

export const notification = {
  success: (message: string, options?: NotificationOptions) => {
    const Icon = icons.success;
    toast.success(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      icon: <Icon className={`h-4 w-4 ${colors.success}`} />,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  },

  error: (message: string, options?: NotificationOptions) => {
    const Icon = icons.error;
    toast.error(message, {
      description: options?.description,
      duration: options?.duration || 6000,
      icon: <Icon className={`h-4 w-4 ${colors.error}`} />,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  },

  warning: (message: string, options?: NotificationOptions) => {
    const Icon = icons.warning;
    toast.warning(message, {
      description: options?.description,
      duration: options?.duration || 5000,
      icon: <Icon className={`h-4 w-4 ${colors.warning}`} />,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  },

  info: (message: string, options?: NotificationOptions) => {
    const Icon = icons.info;
    toast.info(message, {
      description: options?.description,
      duration: options?.duration || 4000,
      icon: <Icon className={`h-4 w-4 ${colors.info}`} />,
      action: options?.action ? {
        label: options.action.label,
        onClick: options.action.onClick,
      } : undefined,
    });
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return toast.promise(promise, {
      loading,
      success,
      error,
    });
  },
};
