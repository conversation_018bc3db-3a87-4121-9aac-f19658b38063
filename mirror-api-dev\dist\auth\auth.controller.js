"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_login_dto_1 = require("./dto/auth-login.dto");
const auth_forget_dto_1 = require("./dto/auth-forget.dto");
const auth_reset_dto_1 = require("./dto/auth-reset.dto");
const auth_service_1 = require("./auth.service");
const auth_guard_1 = require("./auth.guard");
const auth_decorator_1 = require("./auth.decorator");
const throttler_1 = require("@nestjs/throttler");
const auth_teste_dto_1 = require("./dto/auth-teste.dto");
const auth_refresh_dto_1 = require("./dto/auth-refresh.dto");
let AuthController = class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async login({ user, password }) {
        return this.authService.login(user, password);
    }
    async forget({ email }, res) {
        return res
            .status(common_1.HttpStatus.OK)
            .json(await this.authService.forget(email));
    }
    async reset({ token, password }) {
        return this.authService.reset(token, password);
    }
    async newPass({ token, password }) {
        return this.authService.newPass(token, password);
    }
    async refresh({ refresh_token }) {
        return this.authService.refreshToken(refresh_token);
    }
    async teste(auth) {
        return { auth };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)("sign-in"),
    (0, throttler_1.Throttle)({ default: { limit: 3, ttl: 60000 } }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_login_dto_1.AuthLoginDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)("forgot"),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_forget_dto_1.AuthForgetDTO, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forget", null);
__decorate([
    (0, common_1.Post)("reset"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_reset_dto_1.AuthResetDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "reset", null);
__decorate([
    (0, common_1.Post)("new-password"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_reset_dto_1.AuthResetDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "newPass", null);
__decorate([
    (0, common_1.Post)("refresh"),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_refresh_dto_1.AuthRefreshDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refresh", null);
__decorate([
    (0, common_1.Post)("teste"),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard, auth_guard_1.AuthGuard),
    __param(0, (0, auth_decorator_1.Auth)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_teste_dto_1.AuthTesteDTO]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "teste", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)("auth"),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map