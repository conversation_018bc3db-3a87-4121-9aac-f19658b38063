"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serialize = void 0;
exports.serialize = {
    pick(obj, attributesToPick) {
        if (!obj) {
            return {};
        }
        const pickedObj = {};
        attributesToPick.forEach((attribute) => {
            if (Object.prototype.hasOwnProperty.call(obj, attribute)) {
                pickedObj[attribute] = obj[attribute];
            }
        });
        return pickedObj;
    },
    omit(obj, attributesToRemove) {
        if (!obj) {
            return {};
        }
        const serializedObj = { ...obj };
        attributesToRemove.forEach((attribute) => {
            if (Object.prototype.hasOwnProperty.call(serializedObj, attribute)) {
                delete serializedObj[attribute];
            }
        });
        return serializedObj;
    },
};
//# sourceMappingURL=serialize.js.map