"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const prisma_service_1 = require("../prisma/prisma.service");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const bcryptjs_1 = require("bcryptjs");
const env_1 = require("../env");
const mail_service_1 = require("../mail/mail.service");
const uuid_1 = require("uuid");
let AuthService = class AuthService {
    jwtService;
    prisma;
    env;
    mailService;
    constructor(jwtService, prisma, env, mailService) {
        this.jwtService = jwtService;
        this.prisma = prisma;
        this.env = env;
        this.mailService = mailService;
    }
    createToken(user) {
        const access_token = this.jwtService.sign({ sub: user.id, type: "access" }, {
            expiresIn: this.env.get("JWT_EXPIRES_IN"),
        });
        const refresh_token = this.jwtService.sign({ sub: user.id, type: "refresh" }, {
            expiresIn: this.env.get("JWT_REFRESH_EXPIRES_IN"),
        });
        return {
            access_token,
            refresh_token,
        };
    }
    checkToken(token, type) {
        try {
            const payload = this.jwtService.verify(token);
            if (type && payload.type !== type) {
                throw new common_1.UnauthorizedException(`Invalid ${payload.type} token`);
            }
            return payload;
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error);
        }
    }
    async refreshToken(refresh_token) {
        const { sub } = this.checkToken(refresh_token, "refresh");
        const user = await this.prisma.user.findUniqueOrThrow({
            where: { id: sub, status: true },
        });
        if (!sub) {
            throw new common_1.UnauthorizedException("Invalid refresh token");
        }
        return this.createToken(user);
    }
    async login(user, password) {
        const userExists = await this.prisma.user.findUnique({
            where: {
                email: user,
            },
        });
        if (!userExists) {
            throw new common_1.BadRequestException("Usuário e/ou senha inválidos");
        }
        const checkPassword = await (0, bcryptjs_1.compare)(password + userExists.salt, userExists.password);
        if (!checkPassword) {
            throw new common_1.BadRequestException("Usuário e/ou senha inválidos");
        }
        return this.createToken(userExists);
    }
    async forget(email) {
        let forgotToken = await this.prisma.forgotToken.findUnique({
            where: { email },
        });
        if (forgotToken && forgotToken.expires_at > new Date()) {
            return {
                message: "Você já solicitou uma recuperação de senha. Por favor, aguarde 30 minutos para solicitar uma nova recuperação de senha.",
            };
        }
        await this.prisma.forgotToken.deleteMany({
            where: {
                expires_at: {
                    lte: new Date(),
                },
            },
        });
        const user = await this.prisma.user.findUnique({
            where: { email },
        });
        forgotToken = await this.prisma.forgotToken.create({
            data: {
                email,
                user_id: user ? user.id : null,
                token: (0, uuid_1.v7)(),
                expires_at: new Date(Date.now() + 10 * 60 * 1000),
            },
        });
        if (!user) {
            return {
                message: "Se você possui uma conta, um email foi enviado para você com as instruções para redefinir sua senha.",
            };
        }
        await this.mailService.sendTokenForget(user, forgotToken.token);
        return {
            message: "Se você possui uma conta, um email foi enviado para você com as instruções para redefinir sua senha.",
        };
    }
    async newPass(token, password) {
        const userToken = await this.prisma.userToken.findUnique({
            where: {
                token,
                type: "NEW_PASSWORD",
                expires_at: {
                    gt: new Date(),
                },
            },
            include: { user: true },
        });
        if (!userToken) {
            throw new common_1.BadRequestException("Token inválido ou expirado");
        }
        const hashedPassword = await (0, bcryptjs_1.hash)(password + userToken.user?.salt, 8);
        await this.prisma.user.update({
            where: { id: userToken.user_id },
            data: { password: hashedPassword },
        });
        await this.prisma.userToken.deleteMany({
            where: {
                OR: [{ token }, { expires_at: { lte: new Date() } }],
            },
        });
        await this.mailService.sendPasswordChanged(userToken.user);
        return {
            message: "Senha alterada com sucesso",
        };
    }
    async reset(token, password) {
        const forgotToken = await this.prisma.forgotToken.findUnique({
            where: {
                token,
                user_id: { not: null },
                expires_at: {
                    gt: new Date(),
                },
            },
            include: { user: true },
        });
        if (!forgotToken) {
            throw new common_1.BadRequestException("Token inválido ou expirado");
        }
        const hashedPassword = await (0, bcryptjs_1.hash)(password + forgotToken.user?.salt, 8);
        await this.prisma.user.update({
            where: { id: forgotToken.user_id },
            data: { password: hashedPassword },
        });
        await this.prisma.forgotToken.deleteMany({
            where: {
                OR: [{ token }, { expires_at: { lte: new Date() } }],
            },
        });
        await this.mailService.sendPasswordChanged(forgotToken.user);
        return {
            message: "Senha alterada com sucesso",
        };
    }
    async fetchAllRoles() {
        return this.prisma.role.findMany();
    }
    async getRoleById(id) {
        return this.prisma.role.findUnique({
            where: { id },
        });
    }
    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });
        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }
    async createRole(data) {
        await this.checkRoleIsUnique(data);
        return await this.prisma.role.create({
            data,
        });
    }
    async updateRole(id, data) {
        await this.checkRoleIsUnique(data, id);
        return await this.prisma.role.update({
            where: { id },
            data,
        });
    }
    async deleteRole(id) {
        return await this.prisma.role.delete({
            where: { id },
        });
    }
    async checkRoleIsUnique(data, id) {
        const { name, slug } = data;
        await this.prisma.role
            .findFirst({
            where: {
                name,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O nome informado já está em uso");
            }
        });
        await this.prisma.role
            .findFirst({
            where: {
                slug,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O slug informado já está em uso");
            }
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService,
        env_1.EnvService,
        mail_service_1.MailService])
], AuthService);
//# sourceMappingURL=auth.service.js.map