"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpModule = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const http_service_1 = require("./http.service");
let HttpModule = class HttpModule {
};
exports.HttpModule = HttpModule;
exports.HttpModule = HttpModule = __decorate([
    (0, common_1.Module)({
        imports: [
            axios_1.HttpModule.registerAsync({
                useFactory: async () => ({
                    timeout: 9000,
                    headers: {
                        withCredentials: true,
                        accessControlAllowOrigin: "*",
                        accessControlAllowHeader: "Origin, X-Requested-With, Content-Type, Accept",
                        "Content-Type": "application/json",
                    },
                }),
            }),
        ],
        providers: [http_service_1.HttpService],
        exports: [http_service_1.HttpService],
    })
], HttpModule);
//# sourceMappingURL=http.module.js.map