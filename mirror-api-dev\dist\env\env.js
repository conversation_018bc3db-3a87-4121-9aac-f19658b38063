"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.envSchema = void 0;
const zod_1 = require("zod");
exports.envSchema = zod_1.z.object({
    APP_NAME: zod_1.z.string(),
    APP_CORS: zod_1.z.string().default("*"),
    APP_URL: zod_1.z.string(),
    API_URL: zod_1.z.string(),
    DATABASE_URL: zod_1.z.string(),
    PORT: zod_1.z.coerce.number().optional().default(3333),
    JWT_PRIVATE_KEY: zod_1.z.string(),
    JWT_PUBLIC_KEY: zod_1.z.string(),
    JWT_EXPIRES_IN: zod_1.z.string().default("1h"),
    JWT_REFRESH_EXPIRES_IN: zod_1.z.string().default("90m"),
    THROTTLE_TTL: zod_1.z.coerce.number().optional().default(60000),
    THROTTLE_LIMIT: zod_1.z.coerce.number().optional().default(60),
    MAIL_DRIVER: zod_1.z.string().default("smtp"),
    MAIL_HOST: zod_1.z.string(),
    MAIL_PORT: zod_1.z.coerce.number().optional().default(2525),
    MAIL_USER: zod_1.z.string(),
    MAIL_PASS: zod_1.z.string(),
    MAIL_FROM: zod_1.z.string(),
    NODE_ENV: zod_1.z.string().default("development"),
    AWS_REGION: zod_1.z.string().default("us-east-1 "),
    AWS_ACCESS_KEY_ID: zod_1.z.string(),
    AWS_SECRET_ACCESS_KEY: zod_1.z.string(),
    ENABLE_LOGGING: zod_1.z.string(),
    API_SMS_URL: zod_1.z.string(),
    API_SMS_AUTH: zod_1.z.string(),
});
//# sourceMappingURL=env.js.map