import { ComponentProps, ReactNode } from "react";
import { useFormContext } from "react-hook-form";
import { cn } from "@/lib/utils";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

interface EnhancedFieldProps extends ComponentProps<"div"> {
  name: string;
  label: string;
  children: ReactNode;
  description?: string;
  required?: boolean;
  showValidationIcon?: boolean;
}

export function EnhancedField({
  name,
  label,
  children,
  description,
  required = false,
  showValidationIcon = true,
  className,
  ...props
}: EnhancedFieldProps) {
  const {
    formState: { errors, touchedFields },
    watch,
  } = useFormContext();

  const fieldValue = watch(name);
  const hasError = !!errors[name];
  const isTouched = !!touchedFields[name];
  const hasValue = fieldValue !== undefined && fieldValue !== "" && fieldValue !== null;
  const isValid = isTouched && hasValue && !hasError;

  const getValidationIcon = () => {
    if (!showValidationIcon || !isTouched) return null;
    
    if (hasError) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }
    
    if (isValid) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    
    return null;
  };

  return (
    <div className={cn("space-y-2", className)} {...props}>
      <div className="flex items-center justify-between">
        <label
          htmlFor={name}
          className={cn(
            "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
            hasError && "text-destructive",
            isValid && "text-green-600"
          )}
        >
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
        {getValidationIcon()}
      </div>
      
      <div className="relative">
        {children}
      </div>
      
      {description && !hasError && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      {hasError && (
        <div className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3 text-destructive" />
          <p className="text-xs text-destructive">
            {errors[name]?.message?.toString()}
          </p>
        </div>
      )}
    </div>
  );
}

// Componente wrapper para inputs com validação visual
export function ValidatedInput({
  name,
  label,
  description,
  required,
  showValidationIcon,
  className,
  ...inputProps
}: EnhancedFieldProps & ComponentProps<"input">) {
  const { register, formState: { errors } } = useFormContext();
  
  return (
    <EnhancedField
      name={name}
      label={label}
      description={description}
      required={required}
      showValidationIcon={showValidationIcon}
      className={className}
    >
      <input
        id={name}
        {...register(name)}
        {...inputProps}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          errors[name] && "border-destructive focus-visible:ring-destructive",
          inputProps.className
        )}
      />
    </EnhancedField>
  );
}
