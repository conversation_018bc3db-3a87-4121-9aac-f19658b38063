"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const cache_config_1 = require("./cache.config");
let CacheService = class CacheService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async get(key) {
        const [cache] = await this.prisma.$queryRaw `
        /** busca o cache */
        SELECT value
        FROM _cache
        WHERE key = ${key}
        AND (expires_at IS NULL OR expires_at > NOW());
        `;
        return cache ? cache.value : null;
    }
    async set({ key, value, ttl }) {
        const expires_at = ttl
            ? new Date(Date.now() + ttl)
            : new Date(Date.now() + cache_config_1.cacheConfig.defaultTTL);
        await this.prisma.cache.upsert({
            where: { key },
            create: {
                key,
                value,
                expires_at,
            },
            update: {
                value,
                expires_at,
            },
        });
    }
    async delete(key) {
        await this.prisma.cache.deleteMany({ where: { key } });
    }
    async cleanupExpiredCache() {
        await this.prisma.cache.deleteMany({
            where: { expires_at: { lte: new Date() } },
        });
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CacheService);
//# sourceMappingURL=cache.service.js.map