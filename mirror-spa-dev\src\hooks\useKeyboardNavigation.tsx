import { useEffect, useCallback } from "react";

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: () => void;
  description: string;
}

interface UseKeyboardNavigationProps {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
}

export function useKeyboardNavigation({ 
  shortcuts, 
  enabled = true 
}: UseKeyboardNavigationProps) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    const matchingShortcut = shortcuts.find(shortcut => {
      const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
      const ctrlMatch = (shortcut.ctrlKey ?? false) === event.ctrlKey;
      const altMatch = (shortcut.altKey ?? false) === event.altKey;
      const shiftMatch = (shortcut.shiftKey ?? false) === event.shiftKey;
      
      return keyMatch && ctrlMatch && altMatch && shiftMatch;
    });

    if (matchingShortcut) {
      event.preventDefault();
      matchingShortcut.action();
    }
  }, [shortcuts, enabled]);

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [handleKeyDown, enabled]);

  return { shortcuts };
}

// Hook específico para navegação em tabelas
export function useTableKeyboardNavigation({
  onEdit,
  onDelete,
  onAdd,
  enabled = true,
}: {
  onEdit?: () => void;
  onDelete?: () => void;
  onAdd?: () => void;
  enabled?: boolean;
}) {
  const shortcuts: KeyboardShortcut[] = [
    ...(onAdd ? [{
      key: 'n',
      ctrlKey: true,
      action: onAdd,
      description: 'Ctrl+N: Adicionar novo item'
    }] : []),
    ...(onEdit ? [{
      key: 'e',
      ctrlKey: true,
      action: onEdit,
      description: 'Ctrl+E: Editar item selecionado'
    }] : []),
    ...(onDelete ? [{
      key: 'Delete',
      action: onDelete,
      description: 'Delete: Excluir item selecionado'
    }] : []),
  ];

  return useKeyboardNavigation({ shortcuts, enabled });
}

// Hook para navegação em formulários
export function useFormKeyboardNavigation({
  onSave,
  onCancel,
  enabled = true,
}: {
  onSave?: () => void;
  onCancel?: () => void;
  enabled?: boolean;
}) {
  const shortcuts: KeyboardShortcut[] = [
    ...(onSave ? [{
      key: 's',
      ctrlKey: true,
      action: onSave,
      description: 'Ctrl+S: Salvar formulário'
    }] : []),
    ...(onCancel ? [{
      key: 'Escape',
      action: onCancel,
      description: 'Esc: Cancelar'
    }] : []),
  ];

  return useKeyboardNavigation({ shortcuts, enabled });
}
