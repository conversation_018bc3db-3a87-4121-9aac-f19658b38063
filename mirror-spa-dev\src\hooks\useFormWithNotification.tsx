import { useForm, UseFormProps, FieldValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { notification } from "@/components/notification-system";
import { useState } from "react";

interface UseFormWithNotificationProps<T extends FieldValues> extends UseFormProps<T> {
  schema: z.ZodSchema<T>;
  onSubmit: (data: T) => Promise<void>;
  successMessage?: string;
  errorMessage?: string;
}

export function useFormWithNotification<T extends FieldValues>({
  schema,
  onSubmit,
  successMessage = "Operação realizada com sucesso!",
  errorMessage = "Erro ao realizar operação",
  ...formProps
}: UseFormWithNotificationProps<T>) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<T>({
    resolver: zodResolver(schema),
    ...formProps,
  });

  const handleSubmit = form.handleSubmit(async (data: T) => {
    try {
      setIsSubmitting(true);
      await onSubmit(data);
      notification.success(successMessage);
      form.reset();
    } catch (error: any) {
      console.error("Erro no formulário:", error);
      
      // Tratamento de erros específicos
      if (error.response?.status === 422) {
        // Erros de validação do servidor
        const validationErrors = error.response.data.errors;
        Object.keys(validationErrors).forEach((field) => {
          form.setError(field as any, {
            message: validationErrors[field][0],
          });
        });
      } else {
        notification.error(
          errorMessage,
          {
            description: error.message || "Tente novamente mais tarde",
          }
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  });

  return {
    ...form,
    handleSubmit,
    isSubmitting,
  };
}

// Hook específico para formulários de criação/edição
export function useCrudForm<T extends FieldValues>({
  schema,
  onSubmit,
  isEditing = false,
  ...formProps
}: UseFormWithNotificationProps<T> & { isEditing?: boolean }) {
  return useFormWithNotification({
    schema,
    onSubmit,
    successMessage: isEditing 
      ? "Item atualizado com sucesso!" 
      : "Item criado com sucesso!",
    errorMessage: isEditing 
      ? "Erro ao atualizar item" 
      : "Erro ao criar item",
    ...formProps,
  });
}
