"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogInterceptor = void 0;
const operators_1 = require("rxjs/operators");
class LogInterceptor {
    intercept(context, next) {
        const now = Date.now();
        return next
            .handle()
            .pipe((0, operators_1.tap)(() => console.log(`tempo de execução: ${context.getClass().name}.${context.getHandler().name} - ${Date.now() - now}ms`, context.getClass().name)));
    }
}
exports.LogInterceptor = LogInterceptor;
//# sourceMappingURL=log.interceptor.js.map