import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Pa<PERSON>, Settings, Monitor, Sun, Moon } from "lucide-react";
import { useTheme } from "./theme-provider";

interface ThemeCustomizerProps {
  trigger?: React.ReactNode;
}

export function ThemeCustomizer({ trigger }: ThemeCustomizerProps) {
  const { theme, setTheme } = useTheme();
  const [fontSize, setFontSize] = useState(16);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [highContrast, setHighContrast] = useState(false);

  const applyFontSize = (size: number) => {
    document.documentElement.style.fontSize = `${size}px`;
    setFontSize(size);
    localStorage.setItem('font-size', size.toString());
  };

  const applyReducedMotion = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.style.setProperty('--animation-duration', '0s');
    } else {
      document.documentElement.style.removeProperty('--animation-duration');
    }
    setReducedMotion(enabled);
    localStorage.setItem('reduced-motion', enabled.toString());
  };

  const applyHighContrast = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    setHighContrast(enabled);
    localStorage.setItem('high-contrast', enabled.toString());
  };

  const resetToDefaults = () => {
    setTheme('system');
    applyFontSize(16);
    applyReducedMotion(false);
    applyHighContrast(false);
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Palette className="h-4 w-4 mr-2" />
      Personalizar
    </Button>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Personalização do Tema
          </DialogTitle>
          <DialogDescription>
            Ajuste a aparência e acessibilidade da aplicação
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="appearance" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="appearance">Aparência</TabsTrigger>
            <TabsTrigger value="accessibility">Acessibilidade</TabsTrigger>
          </TabsList>

          <TabsContent value="appearance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tema</CardTitle>
                <CardDescription>
                  Escolha entre tema claro, escuro ou automático
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant={theme === "light" ? "default" : "outline"}
                    onClick={() => setTheme("light")}
                    className="h-20 flex-col gap-2"
                  >
                    <Sun className="h-6 w-6" />
                    Claro
                  </Button>
                  <Button
                    variant={theme === "dark" ? "default" : "outline"}
                    onClick={() => setTheme("dark")}
                    className="h-20 flex-col gap-2"
                  >
                    <Moon className="h-6 w-6" />
                    Escuro
                  </Button>
                  <Button
                    variant={theme === "system" ? "default" : "outline"}
                    onClick={() => setTheme("system")}
                    className="h-20 flex-col gap-2"
                  >
                    <Monitor className="h-6 w-6" />
                    Sistema
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Tamanho da Fonte</CardTitle>
                <CardDescription>
                  Ajuste o tamanho do texto para melhor legibilidade
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Tamanho: {fontSize}px</Label>
                  <Slider
                    value={[fontSize]}
                    onValueChange={(value) => applyFontSize(value[0])}
                    min={12}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  Exemplo de texto com o tamanho atual
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="accessibility" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Opções de Acessibilidade</CardTitle>
                <CardDescription>
                  Configure opções para melhorar a acessibilidade
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Reduzir Animações</Label>
                    <p className="text-sm text-muted-foreground">
                      Desabilita animações para reduzir distração
                    </p>
                  </div>
                  <Switch
                    checked={reducedMotion}
                    onCheckedChange={applyReducedMotion}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Alto Contraste</Label>
                    <p className="text-sm text-muted-foreground">
                      Aumenta o contraste para melhor visibilidade
                    </p>
                  </div>
                  <Switch
                    checked={highContrast}
                    onCheckedChange={applyHighContrast}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={resetToDefaults}>
            Restaurar Padrões
          </Button>
          <DialogTrigger asChild>
            <Button>Fechar</Button>
          </DialogTrigger>
        </div>
      </DialogContent>
    </Dialog>
  );
}
