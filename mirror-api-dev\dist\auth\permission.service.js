"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionService = void 0;
const prisma_service_1 = require("../prisma/prisma.service");
const common_1 = require("@nestjs/common");
let PermissionService = class PermissionService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async fetchAllPermissions() {
        const permission = await this.prisma.permission.findMany({
            include: {
                PermissionRole: {
                    select: {
                        role: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });
        return permission.map((permission) => {
            const { PermissionRole, ...rest } = permission;
            return {
                ...rest,
                roles: PermissionRole.map((role) => role.role),
            };
        });
    }
    async createPermission(dto) {
        const { roles, helper_crud, description, ...data } = dto;
        await this.checkPermissionIsUnique(dto);
        if (helper_crud) {
            const name = data.name.split(" ")[1].toLowerCase();
            const slug = data.slug.split("-")[1].toLowerCase();
            await this.prisma.permission.deleteMany({
                where: {
                    name: { endsWith: slug },
                },
            });
            const permissionsData = [
                { name: `Criar ${name}`, slug: `c-${slug}`, description },
                { name: `Listar ${name}`, slug: `r-${slug}`, description },
                { name: `Editar ${name}`, slug: `u-${slug}`, description },
                { name: `Excluir ${name}`, slug: `d-${slug}`, description },
            ];
            const permissions = await Promise.all(permissionsData.map((data) => this.prisma.permission.create({ data })));
            for (const permission of permissions) {
                await this.prisma.permissionRole.createMany({
                    data: roles.map((roleId) => ({
                        permission_id: permission.id,
                        role_id: roleId,
                    })),
                });
            }
            return permissions;
        }
        return await this.prisma.$transaction(async (prisma) => {
            const permission = await prisma.permission.create({
                data: {
                    ...data,
                    description,
                },
            });
            await prisma.permissionRole.createMany({
                data: roles.map((roleId) => ({
                    permission_id: permission.id,
                    role_id: roleId,
                })),
            });
            return permission;
        });
    }
    async updatePermission(id, dto) {
        const { roles, name, slug, description, status } = dto;
        await this.checkPermissionIsUnique(dto, id);
        return await this.prisma.$transaction(async (prisma) => {
            const permission = await prisma.permission.update({
                where: { id },
                data: {
                    name,
                    slug,
                    description,
                    status,
                },
            });
            await prisma.permissionRole.deleteMany({
                where: {
                    permission_id: id,
                },
            });
            await prisma.permissionRole.createMany({
                data: roles.map((roleId) => ({
                    permission_id: id,
                    role_id: roleId,
                })),
            });
            return permission;
        });
    }
    async deletePermission(id) {
        return await this.prisma.permission.delete({
            where: { id },
        });
    }
    async checkPermissionIsUnique(data, id) {
        const { name, slug } = data;
        await this.prisma.permission
            .findFirst({
            where: {
                name,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O nome informado já está em uso");
            }
        });
        await this.prisma.permission
            .findFirst({
            where: {
                slug,
                ...(id && { id: { not: id } }),
            },
        })
            .then((result) => {
            if (result) {
                throw new common_1.BadRequestException("O slug informado já está em uso");
            }
        });
    }
};
exports.PermissionService = PermissionService;
exports.PermissionService = PermissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PermissionService);
//# sourceMappingURL=permission.service.js.map