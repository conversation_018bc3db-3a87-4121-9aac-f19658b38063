"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const axios_2 = require("axios");
const rxjs_1 = require("rxjs");
let HttpService = class HttpService {
    httpService;
    constructor(httpService) {
        this.httpService = httpService;
    }
    async get(url, config) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, config));
            return { status: response.status, data: response.data };
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                const response = error.response;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }
            return {
                status: 500,
                data: null,
                error,
            };
        }
    }
    async post(url, data, config) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(url, data, config));
            return { status: response.status, data: response.data };
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                const response = error.response;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }
            return {
                status: 500,
                data: null,
                error,
            };
        }
    }
    async put(url, data, config) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.put(url, data, config));
            return { status: response.status, data: response.data };
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                const response = error.response;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }
            return {
                status: 500,
                data: null,
                error,
            };
        }
    }
    async delete(url) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.delete(url));
            return { status: response.status, data: response.data };
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                const response = error.response;
                return {
                    status: response?.status || 500,
                    data: null,
                    error: error.message,
                };
            }
            return {
                status: 500,
                data: null,
                error,
            };
        }
    }
};
exports.HttpService = HttpService;
exports.HttpService = HttpService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], HttpService);
//# sourceMappingURL=http.service.js.map